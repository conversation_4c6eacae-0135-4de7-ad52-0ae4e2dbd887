import React from 'react';

const Spinner = ({ 
  size = 'md', 
  color = 'blue', 
  thickness = 'normal',
  className = '' 
}) => {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
    '2xl': 'w-20 h-20'
  };

  const colorClasses = {
    blue: 'border-blue-500',
    green: 'border-green-500',
    red: 'border-red-500',
    purple: 'border-purple-500',
    yellow: 'border-yellow-500',
    pink: 'border-pink-500',
    indigo: 'border-indigo-500',
    gray: 'border-gray-500',
    white: 'border-white',
    black: 'border-black'
  };

  const thicknessClasses = {
    thin: 'border-2',
    normal: 'border-4',
    thick: 'border-8'
  };

  return (
    <div 
      className={`
        ${sizeClasses[size]} 
        ${colorClasses[color]} 
        ${thicknessClasses[thickness]}
        border-t-transparent 
        rounded-full 
        animate-spin
        ${className}
      `}
      role="status"
      aria-label="Loading"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
};