'use client';

import { getCategoryCodes } from '@/action/category-action';
import { getNodes } from '@/action/node-action';
import { registerQna } from '@/action/qna-action';
import { getWorkloads } from '@/action/workload-action';
import { CategoryCode } from '@/types/category-code';
import { NodeResponse } from '@/types/node';
import { Qna } from '@/types/qna';
import { WorkloadResponse } from '@/types/workload';
import { useSession } from 'next-auth/react';
import { ChangeEvent, useEffect, useState } from 'react';

import Script from 'next/script';
import AlertModal from '../modal/alert-modal';
import { useLocale, useTranslations } from 'next-intl';

export default function QnaSection() {
  interface TargetValue {
    target: string | number;
    label: string;
  }

  const t_i18n = useTranslations('i18nData');
  const [alertModal, setAlertModal] = useState<boolean>(false);
  const [alertModalData, setAlertModalData] = useState<any>();
  const { data: session } = useSession();
  const [categoryCodes, setCategoryCodes] = useState<CategoryCode[]>([]);
  const [qna, setQna] = useState<Qna | null>(null);
  const [targetList, setTargetList] = useState<TargetValue[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const init = async () => {
      setQna({
        ser: 0,
        owner: session?.user.email ?? '',
        name: '',
        categoryCode: '',
        title: '',
        content: '',
        targetType: null,
        target: null
      });
      const categoryCodeRequest = { startNum: 0, scaleNum: 0, siteMenu: 'QNA' };
      const categoryCodeResponse = await getCategoryCodes(categoryCodeRequest);
      if (categoryCodeResponse.status == 200) {
        setCategoryCodes(categoryCodeResponse.codes);
      } else {
        setCategoryCodes([]);
      }
      setIsInitialized(true);
    };
    init();
  }, []);

  const onLoad = () => {
    window.Kakao.init('0c735d49a02594a64a8489ae1e44371e');
  };

  const chatChannel = () => {
    if (window.Kakao.isInitialized()) {
      window.Kakao.Channel.chat({
        channelPublicId: '_jBeTn'
      });
    }
  };

  const getWorkloadList = async () => {
    const response: WorkloadResponse = await getWorkloads({
      owner: '',
      startNum: 0,
      scaleNum: 0,
      category: '',
      target: '',
      state: '',
      startTime: '',
      endTime: '',
      sortName: 'ser',
      sortType: 'DESC',
      isDedicated: false
    });
    if (response.status == 200) {
      setTargetList(response.workloads.map((item) => ({ label: item.ser + ' - ' + item.description, target: item.ser })));
    } else {
      setTargetList([]);
    }
  };

  const getNodeList = async () => {
    const response: NodeResponse = await getNodes({
      startNum: 0,
      scaleNum: 0,
      owner: '',
      category: '',
      cloud: '',
      gpuSpec: '',
      hostSpec: '',
      state: '',
      startTime: '',
      endTime: '',
      sortName: 'ser',
      sortType: 'DESC',
      isDedicated: false,
      dedicatedNmsp: ''
    });
    if (response.status == 200) {
      setTargetList(response.nodes.map((item) => ({ label: item.name + ' - ' + item.name, target: item.name })));
    } else {
      setTargetList([]);
    }
  };

  useEffect(() => {
    if (qna?.targetType == 'workload') {
      getWorkloadList();
    } else if (qna?.targetType == 'node') {
      getNodeList();
    } else {
      setTargetList([]);
    }
  }, [qna?.targetType]);

  /**
   * @brief INPUT 값 변경
   * @param e
   */
  const onInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.currentTarget || e.target;
    setQna((prevData) => ({ ...prevData, [name]: value }));
  };

  const onSubmit = async () => {
    const response = await registerQna(qna);
    if (response.status == 200) {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: t_i18n('dialog_title_ok'),
        content: t_i18n('qna_register_msg_success'),
        okBtn: t_i18n('but_ok')
      });

      setAlertModal(true);
      setQna(null);
    } else {
      setAlertModalData({
        btnColor: 'btn-primary',
        title: t_i18n('dialog_title_waring'),
        content: t_i18n('qna_register_msg_failed'),
        okBtn: t_i18n('but_ok')
      });

      setAlertModal(true);
    }
  };
  if (isInitialized)
    return (
      <>
        <div className="grid gap-5 lg:gap-5">
          <div className="card pb-7.5">
            <div className="card-body">
              <div className="flex w-full flex-wrap gap-5">
                <div className="grid w-full gap-6 pr-10 lg:w-[70%]">
                  <div className="flex items-center gap-2.5">
                    <label className="form-label w-16">{t_i18n('qna_register_msg_type')}</label>
                    <div className="flex gap-2">
                      {categoryCodes.map((item, index) => (
                        <label className="form-label flex items-center gap-2.5 text-nowrap" key={`category_${index}`}>
                          <input
                            type="radio"
                            className="radio radio-sm"
                            name="categoryCode"
                            value={item.code}
                            checked={item.code === qna?.categoryCode}
                            onChange={onInputChange}
                          />
                          {item.type}
                        </label>
                      ))}
                    </div>
                  </div>
                  <div className="flex items-center gap-2.5">
                    <label className="form-label w-16">{t_i18n('qna_register_msg_name')}</label>
                    <div className="flex w-32 gap-2">
                      <input type="text" className="input input-sm" name="name" value={qna?.name || ''} onChange={onInputChange} />
                    </div>
                  </div>
                  <div className="flex items-center gap-2.5">
                    <label className="form-label w-16">{t_i18n('qna_register_msg_title')}</label>
                    <div className="flex grow gap-2">
                      <input type="text" className="input input-sm" name="title" value={qna?.title || ''} onChange={onInputChange} />
                    </div>
                  </div>
                  <div className="flex items-center gap-2.5">
                    <label className="form-label w-16">{t_i18n('qna_register_msg_target')}</label>
                    <div className="flex gap-2">
                      <select className="select select-sm pr-10" name="targetType" onChange={onInputChange}>
                        <option value="">{t_i18n('qna_register_msg_select')}</option>
                        <option value="workload">{t_i18n('qna_register_msg_target_workload')}</option>
                        <option value="node">{t_i18n('qna_register_msg_target_node')}</option>
                      </select>
                    </div>
                    <div className="flex gap-2">
                      <select className="select select-sm pr-10" name="target" onChange={onInputChange}>
                        <option value="">{t_i18n('qna_register_msg_select')}</option>
                        {targetList.map((item, index) => (
                          <option key={index} value={item.target}>
                            {item.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2.5">
                    <div className="flex justify-end text-xs text-gray-500">{qna?.content.length} / 5000</div>
                    <div className="flex w-full gap-2.5">
                      <label className="form-label w-16">{t_i18n('qna_register_msg_content')}</label>
                      <div className="flex grow gap-2">
                        <textarea
                          className="textarea w-full"
                          name="content"
                          rows={10}
                          value={qna?.content || ''}
                          onChange={onInputChange}
                          maxLength={5000}
                        ></textarea>
                      </div>
                    </div>
                  </div>
                  <div className="flex w-full items-center justify-center gap-2.5">
                    <button className="btn btn-lg btn-primary px-10" onClick={onSubmit}>
                      {t_i18n('qna_register_msg_send_qna')}
                    </button>
                  </div>
                  <div className="grid grid-cols-2 gap-2 lg:gap-5">
                    <button className="group btn btn-light h-auto justify-center px-3 py-3 lg:px-6 lg:py-5">
                      <span className="flex flex-col items-end">
                        <span className="text-md font-medium text-gray-600">FAQ</span>
                      </span>
                    </button>
                    {/* <button className="group btn btn-light h-auto justify-center px-3 py-3 lg:px-6 lg:py-5">
                    <span className="flex flex-col items-end">
                      <span className="text-md font-medium text-gray-600">카카오 채팅</span>
                    </span>
                  </button> */}
                    <button
                      data-tooltip="#gcube_chat_tooltip_content"
                      id="gcube-chat-button"
                      className="group btn btn-light h-auto justify-center px-3 py-3 lg:px-6 lg:py-5"
                      onClick={() => {
                        chatChannel();
                      }}
                    >
                      <span className="flex items-end gap-2">
                        <img src="/da/img/kakaotalk_sharing_btn_medium.png" className="w-5" alt="kakao" />
                        <span className="text-md font-medium text-gray-600">카카오 채팅</span>
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {alertModal && (
          <AlertModal
            alertModal={alertModal}
            alertModalData={alertModalData}
            onCloseModal={() => {
              setAlertModal(false);
            }}
          ></AlertModal>
        )}
        <Script
          src="https://t1.kakaocdn.net/kakao_js_sdk/2.7.4/kakao.min.js"
          integrity="sha384-DKYJZ8NLiK8MN4/C5P2dtSmLQ4KwPaoqAfyA/DfmEc1VDxu4yyC7wy6K1Hs90nka"
          crossOrigin="anonymous"
          onLoad={onLoad}
        ></Script>
      </>
    );
}
